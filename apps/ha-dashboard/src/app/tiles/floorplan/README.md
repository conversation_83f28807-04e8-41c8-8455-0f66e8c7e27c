# Floorplan Component

This directory contains the Floorplan Tile component and its associated assets.

## SVG File Integration

The SVG floorplan file is now stored directly in this component directory, which offers several advantages:
- Keeps related assets close to their components
- Makes the component more self-contained
- Simplifies development and maintenance

### How it Works

1. The SVG file (`floorplan.svg`) is placed in this directory
2. During build, this file is automatically copied to the public directory
3. The component loads it from the root path (`/floorplan.svg`) at runtime

### Fallback Strategy

For backward compatibility, the Floorplan component will attempt to load the SVG from several locations in this order:
1. The path specified in the component configuration
2. `/floorplan.svg` (current approach - direct from root/public folder)
3. `/assets/tiles/floorplan/floorplan.svg` (previous NX assets approach)
4. `/assets/images/floorplan.svg` (older assets folder approach)
5. `/images/floorplan.svg` (alternate public folder approach)

This ensures that the component continues to work even if the SVG is located in a different place.

## Creating Custom Floorplans

To use your own custom floorplan:

1. Replace the `floorplan.svg` file in this directory with your own SVG
2. Make sure your SVG has room/area elements with IDs that start with `room-` if you want interactive elements
3. Keep your SVG file clean and optimized for web use
4. Rebuild the application

## SVG Requirements for Interactive Elements

For interactive floorplans:
- Use IDs with the prefix `room-` for rooms/areas that should be clickable
- Keep the SVG structure simple and clean
- Ensure proper scaling by setting appropriate viewBox dimensions
- Test your SVG for compatibility with the floorplan component

## Troubleshooting

If your floorplan isn't loading correctly:
1. Check console errors in the browser
2. Verify the SVG file was copied to the public directory and is accessible at `/floorplan.svg`
3. Try the reload button which will attempt various paths
4. Ensure your SVG is well-formed and doesn't contain unsupported elements 