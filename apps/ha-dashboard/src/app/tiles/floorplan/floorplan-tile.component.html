<div class="floorplan-container" [class.dashboard-locked]="!isEditingEnabled">
  <div class="floorplan-header">
    <h3>{{ tile.title || 'Floor Plan' }}</h3>
    
    <!-- Room Controls -->
    <div class="floorplan-controls" *ngIf="isEditingEnabled">
      <button class="room-creation-btn" (click)="toggleRoomCreationMode()" title="Create a custom room area">
        <i class="fa fa-draw-polygon"></i> Add Room
      </button>
      
      <!-- Multi-select mode toggle -->
      <button class="multi-select-btn" (click)="toggleMultiSelectMode()" [class.active]="isMultiSelectMode" title="Toggle multi-select mode">
        <i class="fa fa-object-group"></i> {{ isMultiSelectMode ? 'Exit Multi-Select' : 'Multi-Select' }}
      </button>
      
      <!-- Multi-select actions - only shown when in multi-select mode -->
      <div class="multi-select-actions" *ngIf="isMultiSelectMode">
        <span class="selection-count" *ngIf="selectedRooms.size > 0">{{ selectedRooms.size }} room(s) selected</span>
        <button class="edit-selected-btn" (click)="editSelectedRooms()" [disabled]="selectedRooms.size === 0" title="Edit selected rooms">
          <i class="fa fa-edit"></i> Edit
        </button>
        <button class="save-selected-btn" (click)="saveSelectedRooms()" [disabled]="selectedRooms.size === 0" title="Save selected rooms">
          <i class="fa fa-save"></i> Save
        </button>
        <button class="clear-selection-btn" (click)="clearRoomSelection()" [disabled]="selectedRooms.size === 0" title="Clear selection">
          <i class="fa fa-times"></i> Clear
        </button>
      </div>
    </div>
  </div>
  
  <div class="floorplan-content">
    <!-- Loading state -->
    <app-loading-spinner *ngIf="isLoading" [overlay]="true" message="Loading floorplan..."></app-loading-spinner>
    
    <!-- Error notification (if using placeholder) -->
    <div *ngIf="!isLoading && hasError && usePlaceholder" class="error-notification">
      <div class="error-message">{{ errorMessage }}</div>
      <button (click)="reloadImage()" class="reload-button">Reload</button>
    </div>
    
    <!-- Error state (if completely failed) -->
    <app-empty-state
      *ngIf="!isLoading && hasError && !usePlaceholder"
      title="Image Not Found"
      [description]="errorMessage"
      icon="fa fa-exclamation-triangle"
      actionText="Reload"
      (actionClick)="reloadImage()">
    </app-empty-state>
    
    <!-- Placeholder floorplan (if real image failed to load) -->
    <app-floorplan-placeholder *ngIf="!isLoading && usePlaceholder"></app-floorplan-placeholder>
    
    <!-- Floorplan image when loaded successfully -->
    <ng-container *ngIf="!isLoading && !hasError">
      <!-- SVG floorplan - directly embedded if it's an SVG -->
      <div *ngIf="isSvgFile" #svgContainer class="floorplan-svg-container" 
           [innerHTML]="svgContent | safeHtml" 
           (click)="onSvgContainerClick($event)"></div>
      
      <!-- Regular image floorplan (for PNG, JPG, etc.) -->
      <img *ngIf="!isSvgFile" [src]="floorplanImage" alt="Floor Plan" class="floorplan-image">
      
      <!-- View mode indicator - shown only when dashboard is locked -->
      <div *ngIf="!isEditingEnabled && tile.interactive" class="view-mode-indicator">
        <i class="fa fa-search-plus"></i> Click on rooms to view details
      </div>
      
      <!-- Multi-select mode indicator -->
      <div *ngIf="isMultiSelectMode" class="multi-select-indicator">
        <i class="fa fa-object-group"></i> Multi-select mode: Click or drag to select rooms
      </div>
      
      <!-- Markers overlay if the floorplan is interactive -->
      <div *ngIf="tile.interactive && (markers.length > 0 || generatedMarkers.length > 0)" class="floorplan-markers">
        <div 
          *ngFor="let marker of markers" 
          class="floorplan-marker" 
          [ngClass]="[marker.type, marker.entityId ? 'ha-device' : '']"
          [style.left.%]="marker.x"
          [style.top.%]="marker.y"
          [style.background-color]="marker.color"
          [title]="marker.label || marker.id"
          (click)="marker.entityId ? onDeviceMarkerClick(marker) : onMarkerClick(marker)">
          <i *ngIf="marker.icon" [class]="marker.icon"></i>
          <span *ngIf="marker.showState && marker.entityId" class="marker-state">
            {{ getEntityState(marker.entityId) }}
          </span>
        </div>
      </div>
      
      <!-- Home Assistant status indicator -->
      <div *ngIf="!haConnected && tile.haDevices && tile.haDevices.length > 0" 
           class="ha-status-indicator offline">
        <i class="fas fa-exclamation-triangle"></i> Home Assistant Disconnected
      </div>
      
      <!-- Empty markers state -->
      <div *ngIf="tile.interactive && markers.length === 0" class="empty-markers-message">
        <app-empty-state
          title="No Markers"
          description="No interactive markers have been added to this floorplan yet."
          icon="fa fa-map-marker">
        </app-empty-state>
      </div>
    </ng-container>
  </div>
  
  <!-- Room Detail Modal -->
  <div *ngIf="showRoomDetail && selectedRoom" class="room-detail-modal-wrapper">
    <app-room-detail 
      [room]="selectedRoom" 
      [isVisible]="showRoomDetail"
      (close)="closeRoomDetail()">
    </app-room-detail>
  </div>
</div> 