<div class="floorplan-container" [class.dashboard-locked]="!isEditingEnabled">
  <div class="floorplan-header">
    <h3>{{ tile.title || 'Floor Plan' }}</h3>
    
    <!-- Room Controls -->
    <div class="floorplan-controls" *ngIf="isEditingEnabled">
      <button class="room-creation-btn" (click)="toggleRoomCreationMode()" title="Create a custom room area">
        <i class="fa fa-draw-polygon"></i> Add Room
      </button>
      
      <!-- Multi-select mode toggle -->
      <button class="multi-select-btn" (click)="toggleMultiSelectMode()" [class.active]="isMultiSelectMode" title="Toggle multi-select mode">
        <i class="fa fa-object-group"></i> {{ isMultiSelectMode ? 'Exit Multi-Select' : 'Multi-Select' }}
      </button>
      
      <!-- Multi-select actions - only shown when in multi-select mode -->
      <div class="multi-select-actions" *ngIf="isMultiSelectMode">
        <span class="selection-count" *ngIf="selectedRooms.size > 0">{{ selectedRooms.size }} room(s) selected</span>
        <button class="edit-selected-btn" (click)="editSelectedRooms()" [disabled]="selectedRooms.size === 0" title="Edit selected rooms">
          <i class="fa fa-edit"></i> Edit
        </button>
        <button class="save-selected-btn" (click)="saveSelectedRooms()" [disabled]="selectedRooms.size === 0" title="Save selected rooms">
          <i class="fa fa-save"></i> Save
        </button>
        <button class="clear-selection-btn" (click)="clearRoomSelection()" [disabled]="selectedRooms.size === 0" title="Clear selection">
          <i class="fa fa-times"></i> Clear
        </button>
      </div>
    </div>
  </div>
  
  <!-- Floorplan Viewer Component -->
  <app-floorplan-viewer
    [imagePath]="floorplanImage"
    [isEditingEnabled]="isEditingEnabled"
    [interactive]="tile.interactive || false"
    [svgContent]="svgContent"
    (svgContentChange)="onSvgContentChange($event)"
    (svgContainerClick)="onSvgContainerClick($event)"
    (roomClick)="onRoomClick($event.roomId, $event.event)"
    (svgLoaded)="onSvgLoaded($event)">
  </app-floorplan-viewer>

  <!-- Marker Overlay Component -->
  <app-marker-overlay
    *ngIf="tile.interactive"
    [markers]="allMarkers"
    [haEntities]="haEntities"
    [showLabels]="true"
    [isEditingEnabled]="isEditingEnabled"
    [loadingEntityIds]="loadingEntityIds"
    [errorEntityIds]="errorEntityIds"
    (markerClick)="onMarkerClick($event)"
    (deviceMarkerClick)="onDeviceMarkerClick($event)">
  </app-marker-overlay>

  <!-- Multi-select mode indicator -->
  <div *ngIf="isMultiSelectMode" class="multi-select-indicator">
    <i class="fa fa-object-group"></i> Multi-select mode: Click or drag to select rooms
  </div>

  <!-- Home Assistant status indicator -->
  <div *ngIf="!haConnected && tile.haDevices && tile.haDevices.length > 0"
       class="ha-status-indicator offline">
    <i class="fas fa-exclamation-triangle"></i> Home Assistant Disconnected
  </div>

  <!-- Empty markers state -->
  <div *ngIf="tile.interactive && allMarkers.length === 0" class="empty-markers-message">
    <app-empty-state
      title="No Markers"
      description="No interactive markers have been added to this floorplan yet."
      icon="fa fa-map-marker">
    </app-empty-state>
  </div>
  
  <!-- Room Detail Modal -->
  <div *ngIf="showRoomDetail && selectedRoom" class="room-detail-modal-wrapper">
    <app-room-detail 
      [room]="selectedRoom" 
      [isVisible]="showRoomDetail"
      (close)="closeRoomDetail()">
    </app-room-detail>
  </div>
</div> 