# Room Detail Feature for Floorplan

This document explains how to set up interactive rooms in your floorplan that show detailed views when clicked.

## Overview

The floorplan tile now supports clicking on individual rooms to show a detailed view of that room. This is useful for:
- Displaying high-resolution images of specific rooms
- Showing room-specific information and controls
- Providing a zoomed-in view of smaller areas

## How It Works

1. When a user clicks on a room in the floorplan SVG, a modal window appears
2. The modal shows a detailed image of the room along with any associated information
3. Users can view devices in the room and interact with them
4. Clicking "Back to Floor Plan" returns to the main floorplan view

## Setup Instructions

### 1. Prepare Your SVG Floorplan

Make sure your SVG floorplan has properly defined rooms with IDs that follow this convention:

```xml
<path id="room-living" d="M10,10 L50,10 L50,50 L10,50 Z" />
<path id="room-kitchen" d="M60,10 L100,10 L100,50 L60,50 Z" />
```

Note the `room-` prefix - this is required for the system to recognize clickable rooms.

### 2. Prepare Detailed Room Images

Create PNG images for each room and place them in the appropriate directory:

```
/assets/images/rooms/living.png
/assets/images/rooms/kitchen.png
/assets/images/rooms/bathroom.png
```

The filename should match the room ID (without the `room-` prefix).

### 3. Configure Room Data

Update your dashboard configuration to include room details:

```typescript
const floorplanTile: FloorplanTile = {
  cols: 6,
  rows: 12,
  y: 0,
  x: 0,
  type: 'floorplan',
  id: 'floorplan',
  imagePath: '/floorplan.svg',
  title: 'Floor Plan',
  interactive: true,
  rooms: {
    'living': {
      id: 'living',
      name: 'Living Room',
      imagePath: '/assets/images/rooms/living.png',
      type: 'living',
      description: 'Main living area with TV and couch',
      devices: ['Living Room Lights', 'TV', 'Smart Speaker']
    },
    'kitchen': {
      id: 'kitchen',
      name: 'Kitchen',
      imagePath: '/assets/images/rooms/kitchen.png',
      type: 'kitchen',
      description: 'Kitchen with island and appliances',
      devices: ['Kitchen Lights', 'Refrigerator', 'Coffee Maker']
    }
  }
};
```

The keys in the `rooms` object should match the room IDs in your SVG (without the `room-` prefix).

## Automatic Room Detection

If you don't provide room data in the configuration, the system will automatically:

1. Generate a reasonable name from the room ID (e.g., "room-living-room" → "Living Room")
2. Look for a matching PNG image at `/assets/images/rooms/{roomId}.png`

## Troubleshooting

- If clicking on rooms doesn't work, check your SVG to ensure rooms have IDs with the `room-` prefix
- If the room image doesn't appear, verify the path is correct and the image exists
- Make sure the `interactive` property on the FloorplanTile is set to `true`

## Example

Here's a minimal example that demonstrates the feature:

```typescript
// In your component
const floorplanConfig: FloorplanTile = {
  // ... other properties
  interactive: true,
  rooms: {
    'master-bedroom': {
      id: 'master-bedroom',
      name: 'Master Bedroom',
      imagePath: '/assets/images/rooms/master-bedroom.png',
      type: 'bedroom',
      description: 'Primary bedroom with ensuite bathroom'
    }
  }
};
```

This will show a detailed view of the master bedroom when users click on the corresponding area in the floorplan. 