import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FloorplanMarker } from '../../../../models/dashboard.models';
import { HAEntity } from '../../../../services/home-assistant.service';

@Component({
  selector: 'app-marker-overlay',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div *ngIf="markers.length > 0" class="floorplan-markers">
      <div 